"use client";
import { useState } from "react";

const CATEGORIES = [
  { label: "Architectural Elements", count: 199, icon: "https://ext.same-assets.com/4123950039/3695065807.svg" },
  { label: "Design Styles", count: 478, icon: "https://ext.same-assets.com/4123950039/640793450.svg" },
  { label: "Furniture Types", count: 599, icon: "https://ext.same-assets.com/4123950039/488564308.svg" },
  { label: "Lighting", count: 111, icon: "https://ext.same-assets.com/4123950039/4211619074.svg" },
  { label: "Decorative Techniques", count: 322, icon: "https://ext.same-assets.com/4123950039/806734161.svg" },
  { label: "Decorative Objects", count: 240, icon: "https://ext.same-assets.com/4123950039/482324065.svg" },
  { label: "Window Treatments", count: 65, icon: "https://ext.same-assets.com/4123950039/1758428594.svg" },
  { label: "Historical Periods & Movements", count: 150, icon: "https://ext.same-assets.com/4123950039/262110880.svg" },
  { label: "Technical Terms", count: 38, icon: "https://ext.same-assets.com/4123950039/638459150.svg" },
  { label: "Space Planning & Layout", count: 134, icon: "https://ext.same-assets.com/4123950039/3766100564.svg" },
  { label: "Art & Sculpture", count: 30, icon: "https://ext.same-assets.com/4123950039/4027277656.svg" },
  { label: "Fabrication & Craftsmanship", count: 133, icon: "https://ext.same-assets.com/4123950039/2443991763.svg" },
  { label: "Lighting Fixtures & Hardware", count: 17, icon: "https://ext.same-assets.com/4123950039/836210335.svg" },
  { label: "Floor Coverings & Rugs", count: 7, icon: "https://ext.same-assets.com/4123950039/116021900.svg" },
  { label: "Decorating Principles & Elements", count: 330, icon: "https://ext.same-assets.com/4123950039/4043401245.svg" },
  { label: "Textile Techniques", count: 10, icon: "https://ext.same-assets.com/4123950039/592947559.svg" },
  { label: "Kitchen & Bath", count: 37, icon: "https://ext.same-assets.com/4123950039/2131603054.svg" },
  { label: "Storage & Organization", count: 79, icon: "https://ext.same-assets.com/4123950039/2539976037.svg" },
  { label: "Wall & Ceiling Treatments", count: 35, icon: "https://ext.same-assets.com/4123950039/1122812705.svg" },
  { label: "Materials & Textiles", count: 360, icon: "https://ext.same-assets.com/4123950039/4155173108.svg" },
  { label: "Color & Patterns", count: 154, icon: "https://ext.same-assets.com/4123950039/1063461199.svg" },
  { label: "Textiles & Upholstery", count: 252, icon: "https://ext.same-assets.com/4123950039/372762760.svg" },
  { label: "Accessibility & Ergonomics", count: 30, icon: "https://ext.same-assets.com/4123950039/593849566.svg" },
  { label: "Construction & Building", count: 86, icon: "https://ext.same-assets.com/4123950039/3359419651.svg" },
  { label: "Flooring & Carpets", count: 48, icon: "https://ext.same-assets.com/4123950039/108637403.svg" },
  { label: "Wall Treatments & Finishes", count: 157, icon: "https://ext.same-assets.com/4123950039/3021487770.svg" },
  { label: "Outdoor & Garden", count: 14, icon: "https://ext.same-assets.com/4123950039/551721556.svg" },
  { label: "Sustainability & Eco-Friendly Design", count: 69, icon: "https://ext.same-assets.com/4123950039/1729642327.svg" }
];

function slugify(label: string) {
  return label
    .toLowerCase()
    .replace(/&/g, 'and')
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)+/g, '');
}

const getInitialTab = () => 'All';

export default function InteriorGlossaryPage() {
  const [search, setSearch] = useState("");
  const [activeTab, setActiveTab] = useState(getInitialTab());
  const TABS = [
    'All', ...Array(26).fill(undefined).map((_, i) => String.fromCharCode('A'.charCodeAt(0) + i))
  ];

  // Filter by tab, then by search
  let filtered = CATEGORIES;
  if (activeTab !== 'All') {
    filtered = filtered.filter(cat => cat.label.toUpperCase().startsWith(activeTab));
  }
  if (search.trim() !== '') {
    filtered = filtered.filter(cat => cat.label.toLowerCase().includes(search.toLowerCase()));
  }

  return (
    <main className="min-h-screen bg-[#0c4c4a] pb-20 relative font-sans">
      {/* Breadcrumbs stub (for subpages, pixel-perfect) */}
      <nav aria-label="Breadcrumb" className="max-w-6xl mx-auto px-2 sm:px-4 pt-6 pb-2">
        <ol className="flex items-center space-x-2 text-xs md:text-sm text-teal-100/80 font-medium">
          <li>
            <a href="/" className="hover:underline hover:text-white transition">Home</a>
          </li>
          <li>
            <svg className="inline-block w-3 h-3 mx-1 text-teal-200" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M9 5l7 7-7 7" /></svg>
          </li>
          <li className="text-white">Interior Design Glossary</li>
        </ol>
      </nav>
      <section className="max-w-6xl mx-auto px-2 sm:px-4 pb-10 pt-6 md:pt-12 relative">
        {/* NEW badge, hero, and header SVG */}
        <div className="flex items-center gap-4 mb-6 relative">
          <h1 className="text-3xl md:text-4xl font-bold text-white tracking-tight leading-tight">
            Interior Design Explained
          </h1>
          <span
            className="ml-2 px-3 py-1 text-xs rounded-full font-bold bg-yellow-300 text-yellow-800 shadow border border-yellow-200 uppercase tracking-wider"
            style={{
              fontFamily: "Inter, sans-serif",
              letterSpacing: "0.08em",
              fontWeight: 800,
              fontSize: "0.85rem",
              position: "relative",
              top: "-2px",
              boxShadow: "0 2px 8px 0 rgba(255, 220, 40, 0.13)"
            }}
          >
            NEW
          </span>
        </div>
        <div className="flex items-center gap-3 mb-8">
          <div className="text-lg text-white font-medium">
            Learn the language of interior design with our free glossary
          </div>
          {/* Hero SVG, pixel-perfect */}
          <span className="ml-2 select-none pointer-events-none">
            {/* Inline SVG as per RoomAI's hero */}
            <svg width="72" height="40" viewBox="0 0 72 40" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-10 md:h-14">
              <path d="M0 32C8 32 8 8 16 8C24 8 24 32 32 32C40 32 40 8 48 8C56 8 56 32 64 32C68 32 72 28 72 24" stroke="#42af9f" strokeWidth="3" strokeLinecap="round" />
              <circle cx="16" cy="8" r="3" fill="#42af9f" />
              <circle cx="48" cy="8" r="3" fill="#42af9f" />
              <circle cx="64" cy="32" r="3" fill="#42af9f" />
            </svg>
          </span>
        </div>
        {/* Search bar with icon and A-Z tabs */}
        <div className="w-full flex flex-col sm:flex-row sm:items-center gap-5 mb-8">
          <div className="relative flex-1 max-w-lg">
            <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                <circle cx="11" cy="11" r="8" />
                <path d="M21 21l-4.35-4.35" strokeLinecap="round" />
              </svg>
            </span>
            <input
              type="text"
              value={search}
              onChange={e => setSearch(e.target.value)}
              className="w-full pl-10 pr-4 py-2 rounded-lg border-2 border-gray-900 bg-[#134a47] text-white placeholder:text-gray-300 text-base focus:outline-none focus:border-teal-400 transition"
              placeholder="Search all categories..."
              aria-label="Search all categories"
            />
          </div>
          <div className="flex flex-wrap gap-1 justify-center mt-2 sm:mt-0">
            {TABS.map(tab => (
              <button
                type="button"
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`px-3 py-1 rounded-full text-xs font-semibold border transition-all
                  ${tab === activeTab
                    ? "bg-teal-600 border-teal-300 text-white shadow"
                    : "bg-gray-900 border-gray-800 text-teal-300 hover:bg-teal-800/40 focus:bg-teal-800/40"}
                 `}
                aria-pressed={tab === activeTab}
                tabIndex={0}
                style={{
                  minWidth: 32,
                  fontFamily: "Inter, sans-serif",
                  letterSpacing: "0.04em"
                }}
              >{tab}</button>
            ))}
          </div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mt-8">
          {filtered.length === 0 && (
            <div className="col-span-full text-center text-white/90 py-24 text-lg">No categories found.</div>
          )}
          {filtered.map((cat, idx) => (
            <a
              href={`/interior-design-glossary/${slugify(cat.label)}`}
              key={cat.label}
              tabIndex={0}
              className="group flex flex-col justify-between transition-all duration-200 focus:outline-none hover:scale-[1.03] active:scale-97 bg-white/3 border-2 border-[#195b55] hover:border-teal-300 shadow-xl rounded-2xl px-5 pt-8 pb-5 relative overflow-visible focus:ring-2 focus:ring-teal-200"
              style={{ animationDelay: `${0.02 * idx}s`, animationDuration: '0.4s', animationName: "fadein, slideup", animationFillMode: "forwards" }}
              aria-label={`View glossary for ${cat.label}`}
            >
              <div className="flex items-center mb-6 gap-4">
                <span className="rounded-full bg-[#e2efed] border border-[#42af9f] p-3 flex items-center justify-center transition group-hover:shadow-lg group-hover:border-teal-400">
                  <img src={cat.icon} alt="" className="w-8 h-8" />
                </span>
                <span className="flex-1 text-lg font-semibold text-white truncate" style={{fontFamily: "Inter, sans-serif"}}>{cat.label}</span>
              </div>
              <span className="inline-block text-xs px-3 py-1 bg-[#42af9f] text-[#165254] rounded-full font-bold mt-3 self-start tracking-wide shadow"
                style={{fontFamily: "Inter, sans-serif", letterSpacing: "0.04em"}}
              >
                {cat.count} articles
              </span>
              {/* Decorative chevron for nav */}
              <span className="absolute right-5 top-3 text-teal-400 opacity-70 group-hover:translate-x-1 transition-all">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" strokeWidth={2} viewBox="0 0 24 24" aria-hidden="true"><path strokeLinecap="round" strokeLinejoin="round" d="M9 5l7 7-7 7" /></svg>
              </span>
            </a>
          ))}
        </div>
      </section>
      {/* Hamburger nav (if site does it) - stub, not shown on this page */}
      <style jsx global>{`
        html {
          font-family: Inter, ui-sans-serif, system-ui, sans-serif;
        }
        /* Animations for fade/slide-in */
        @keyframes fadein {
          from { opacity: 0; }
          to { opacity: 1; }
        }
        @keyframes slideup {
          from { transform: translateY(16px);}
          to { transform: translateY(0);}
        }
        .active-tab {
          background: #42af9f !important;
          color: #165254 !important;
        }
        /* Card hover/focus/active states */
        a.group:focus-visible {
          outline: 2px solid #42af9f;
          outline-offset: 2px;
        }
        a.group:active {
          transform: scale(0.97);
        }
        /* Remove tap highlight on mobile */
        a, button, input {
          -webkit-tap-highlight-color: transparent;
        }
        /* Responsive tweaks for pixel-perfect mobile */
        @media (max-width: 640px) {
          .group {
            padding-left: 1.1rem !important;
            padding-right: 1.1rem !important;
            padding-top: 1.2rem !important;
            padding-bottom: 1.1rem !important;
          }
          .mb-6 {
            margin-bottom: 1.1rem !important;
          }
        }
        /* Custom scrollbar for tabs */
        .flex-wrap::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </main>
  );
}
