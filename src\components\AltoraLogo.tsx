import React from 'react';

interface AltoraLogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'full' | 'icon' | 'text';
  className?: string;
}

const sizeClasses = {
  sm: { icon: 'w-6 h-6', text: 'text-lg', container: 'gap-1.5' },
  md: { icon: 'w-8 h-8', text: 'text-xl', container: 'gap-2' },
  lg: { icon: 'w-12 h-12', text: 'text-2xl', container: 'gap-3' },
  xl: { icon: 'w-16 h-16', text: 'text-4xl', container: 'gap-4' }
};

export const AltoraLogo: React.FC<AltoraLogoProps> = ({ 
  size = 'md', 
  variant = 'full', 
  className = '' 
}) => {
  const sizes = sizeClasses[size];

  const LogoIcon = () => (
    <div className={`${sizes.icon} bg-gradient-to-br from-teal-400 via-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg`}>
      <svg 
        viewBox="0 0 24 24" 
        className="w-3/5 h-3/5 text-white" 
        fill="currentColor"
      >
        {/* Stylized "A" with production design elements */}
        <path d="M12 2L3 22h3.5l1.5-4h8l1.5 4H21L12 2zm-2.5 12l2.5-6.5L14.5 14h-5z"/>
        {/* Film strip accent */}
        <rect x="2" y="20" width="2" height="2" opacity="0.7"/>
        <rect x="6" y="20" width="2" height="2" opacity="0.7"/>
        <rect x="16" y="20" width="2" height="2" opacity="0.7"/>
        <rect x="20" y="20" width="2" height="2" opacity="0.7"/>
      </svg>
    </div>
  );

  const LogoText = () => (
    <span className={`${sizes.text} font-bold text-white tracking-tight`}>
      Altora<span className="font-serif italic text-teal-400 ml-0.5">.Design</span>
    </span>
  );

  if (variant === 'icon') {
    return <LogoIcon />;
  }

  if (variant === 'text') {
    return <LogoText />;
  }

  return (
    <div className={`flex items-center ${sizes.container} ${className}`}>
      <LogoIcon />
      <LogoText />
    </div>
  );
};

// Professional SVG Logo for high-quality usage
export const AltoraLogoSVG: React.FC<{ width?: number; height?: number; className?: string }> = ({ 
  width = 200, 
  height = 60, 
  className = '' 
}) => (
  <svg 
    width={width} 
    height={height} 
    viewBox="0 0 200 60" 
    className={className}
    xmlns="http://www.w3.org/2000/svg"
  >
    <defs>
      {/* Gradient for the icon */}
      <linearGradient id="altoraGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#14B8A6" />
        <stop offset="50%" stopColor="#3B82F6" />
        <stop offset="100%" stopColor="#8B5CF6" />
      </linearGradient>
      
      {/* Shadow filter */}
      <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
        <feDropShadow dx="0" dy="2" stdDeviation="3" floodColor="#000000" floodOpacity="0.3"/>
      </filter>
    </defs>
    
    {/* Logo Icon */}
    <g transform="translate(10, 10)">
      {/* Background circle with gradient */}
      <rect 
        x="0" 
        y="0" 
        width="40" 
        height="40" 
        rx="8" 
        fill="url(#altoraGradient)" 
        filter="url(#shadow)"
      />
      
      {/* Stylized "A" */}
      <path 
        d="M20 8L12 32h4l1.5-4h5l1.5 4h4L20 8zm-2 16l2-6 2 6h-4z" 
        fill="white"
      />
      
      {/* Film strip elements */}
      <rect x="2" y="34" width="2" height="2" fill="white" opacity="0.7"/>
      <rect x="6" y="34" width="2" height="2" fill="white" opacity="0.7"/>
      <rect x="32" y="34" width="2" height="2" fill="white" opacity="0.7"/>
      <rect x="36" y="34" width="2" height="2" fill="white" opacity="0.7"/>
    </g>
    
    {/* Logo Text */}
    <text
      x="65"
      y="38"
      fontFamily="Inter, system-ui, sans-serif"
      fontSize="28"
      fontWeight="700"
      fill="white"
      letterSpacing="-0.02em"
    >
      Altora
    </text>
    <text
      x="155"
      y="38"
      fontFamily="serif"
      fontSize="28"
      fontWeight="400"
      fontStyle="italic"
      fill="#14B8A6"
      letterSpacing="-0.02em"
    >
      .Design
    </text>
    
    {/* Tagline */}
    <text 
      x="65" 
      y="52" 
      fontFamily="Inter, system-ui, sans-serif" 
      fontSize="10" 
      fontWeight="400" 
      fill="#94A3B8" 
      letterSpacing="0.05em"
    >
      PRODUCTION DESIGN STUDIO
    </text>
  </svg>
);

// Favicon/App Icon version
export const AltoraFavicon: React.FC<{ size?: number }> = ({ size = 32 }) => (
  <svg 
    width={size} 
    height={size} 
    viewBox="0 0 32 32" 
    xmlns="http://www.w3.org/2000/svg"
  >
    <defs>
      <linearGradient id="faviconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#14B8A6" />
        <stop offset="50%" stopColor="#3B82F6" />
        <stop offset="100%" stopColor="#8B5CF6" />
      </linearGradient>
    </defs>
    
    {/* Background */}
    <rect width="32" height="32" rx="6" fill="url(#faviconGradient)"/>
    
    {/* Stylized "A" */}
    <path 
      d="M16 6L10 26h3l1-3h4l1 3h3L16 6zm-1.5 12L16 14l1.5 4h-3z" 
      fill="white"
    />
    
    {/* Film strip accent */}
    <rect x="2" y="28" width="1.5" height="1.5" fill="white" opacity="0.6"/>
    <rect x="5" y="28" width="1.5" height="1.5" fill="white" opacity="0.6"/>
    <rect x="25.5" y="28" width="1.5" height="1.5" fill="white" opacity="0.6"/>
    <rect x="28.5" y="28" width="1.5" height="1.5" fill="white" opacity="0.6"/>
  </svg>
);

export default AltoraLogo;
